<?php
echo "PHP is working!\n";
echo "Current directory: " . getcwd() . "\n";
echo "PHP version: " . phpversion() . "\n";

// 测试autoload
require_once 'vendor/autoload.php';

try {
    $config = Filebed\Config::getInstance();
    echo "Config loaded successfully!\n";
    echo "App name: " . $config->get('app_name') . "\n";
    
    $webdavConfigs = $config->getWebDAVConfigs();
    echo "WebDAV configs found: " . count($webdavConfigs) . "\n";
    
    foreach ($webdavConfigs as $alias => $config) {
        echo "- $alias: " . $config['name'] . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
