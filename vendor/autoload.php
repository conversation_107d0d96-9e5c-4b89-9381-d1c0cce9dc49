<?php

// 简单的自动加载器
spl_autoload_register(function ($class) {
    // 将命名空间转换为文件路径
    $prefix = 'Filebed\\';
    $base_dir = __DIR__ . '/../src/';
    
    // 检查类是否使用了命名空间前缀
    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }
    
    // 获取相对类名
    $relative_class = substr($class, $len);
    
    // 将命名空间前缀替换为基础目录，将命名空间分隔符替换为目录分隔符
    $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';
    
    // 如果文件存在，则包含它
    if (file_exists($file)) {
        require $file;
    }
});

// 加载dotenv的简化版本
if (!function_exists('loadEnv')) {
    function loadEnv($path) {
        if (!file_exists($path)) {
            return;
        }
        
        $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos(trim($line), '#') === 0) {
                continue;
            }
            
            if (strpos($line, '=') !== false) {
                list($name, $value) = explode('=', $line, 2);
                $name = trim($name);
                $value = trim($value);
                
                // 移除引号
                if (preg_match('/^"(.*)"$/', $value, $matches)) {
                    $value = $matches[1];
                } elseif (preg_match("/^'(.*)'$/", $value, $matches)) {
                    $value = $matches[1];
                }
                
                if (!array_key_exists($name, $_ENV)) {
                    $_ENV[$name] = $value;
                }
            }
        }
    }
    
    // 加载.env文件
    loadEnv(__DIR__ . '/../.env');
}
