# 应用配置
APP_NAME="PHP文件床"
APP_DEBUG=false
APP_URL=http://localhost
UPLOAD_MAX_SIZE=100M
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,bmp,webp,pdf,txt,doc,docx,zip,rar,7z,mp4,mp3,avi

# 默认WebDAV配置
DEFAULT_WEBDAV=teracloud

# WebDAV配置 - TeraCloud示例
WEBDAV_TERACLOUD_NAME="TeraCloud存储"
WEBDAV_TERACLOUD_URL="https://kamo.teracloud.jp/dav/"
WEBDAV_TERACLOUD_USERNAME="tu_chuang"
WEBDAV_TERACLOUD_PASSWORD="CgLjsmZaz8UWwA9J"
WEBDAV_TERACLOUD_BASE_PATH="/uploads/"

# 可以添加更多WebDAV配置
# WEBDAV_BACKUP_NAME="备份存储"
# WEBDAV_BACKUP_URL="https://your-webdav-server.com/dav/"
# WEBDAV_BACKUP_USERNAME="your_username"
# WEBDAV_BACKUP_PASSWORD="your_password"
# WEBDAV_BACKUP_BASE_PATH="/files/"

# 安全配置
API_KEY_REQUIRED=false
API_KEY=""
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_TIME_WINDOW=3600

# 日志配置
LOG_ENABLED=true
LOG_LEVEL=info
LOG_FILE=logs/app.log
