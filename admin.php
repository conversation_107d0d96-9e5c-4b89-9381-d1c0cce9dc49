<?php
session_start();
require_once 'vendor/autoload.php';

use Filebed\Config;
use Filebed\FileUploader;

$config = Config::getInstance();
$uploader = new FileUploader();

// 简单的密码保护
$adminPassword = $_ENV['ADMIN_PASSWORD'] ?? 'admin123';
$isAuthenticated = false;

if (isset($_POST['password'])) {
    if ($_POST['password'] === $adminPassword) {
        $_SESSION['admin_authenticated'] = true;
        $isAuthenticated = true;
    }
} elseif (isset($_SESSION['admin_authenticated']) && $_SESSION['admin_authenticated']) {
    $isAuthenticated = true;
}

if (!$isAuthenticated) {
    ?>
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>管理员登录</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body class="bg-light">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card mt-5">
                        <div class="card-header">
                            <h4>管理员登录</h4>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                <div class="mb-3">
                                    <label for="password" class="form-label">密码</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                                <button type="submit" class="btn btn-primary">登录</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// 处理AJAX请求
if (isset($_GET['action'])) {
    header('Content-Type: application/json');

    // 检查认证状态
    if (!$isAuthenticated) {
        echo json_encode(['success' => false, 'error' => '未认证']);
        exit;
    }

    switch ($_GET['action']) {
        case 'test_webdav':
            $alias = $_GET['alias'] ?? '';
            $result = $uploader->testWebDAVConnection($alias);
            echo json_encode($result);
            exit;
            
        case 'get_logs':
            $logFile = $config->get('log_file', 'logs/app.log');
            $logs = [];
            
            if (file_exists($logFile)) {
                $lines = file($logFile, FILE_IGNORE_NEW_LINES);
                $lines = array_reverse(array_slice($lines, -50)); // 最近50条
                
                foreach ($lines as $line) {
                    $logData = json_decode($line, true);
                    if ($logData) {
                        $logs[] = $logData;
                    }
                }
            }
            
            echo json_encode(['success' => true, 'logs' => $logs]);
            exit;
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件床管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-dark bg-dark">
        <div class="container">
            <span class="navbar-brand">文件床管理后台</span>
            <a href="?logout=1" class="btn btn-outline-light btn-sm">退出</a>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 系统状态 -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-info-circle"></i> 系统信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>应用名称:</strong> <?= htmlspecialchars($config->get('app_name')) ?></p>
                                <p><strong>调试模式:</strong> <?= $config->get('app_debug') ? '开启' : '关闭' ?></p>
                                <p><strong>最大上传大小:</strong> <?= $config->formatFileSize($config->get('upload_max_size')) ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>允许的文件类型:</strong> <?= implode(', ', $config->get('allowed_extensions')) ?></p>
                                <p><strong>默认WebDAV:</strong> <?= htmlspecialchars($config->get('default_webdav')) ?></p>
                                <p><strong>日志状态:</strong> <?= $config->get('log_enabled') ? '开启' : '关闭' ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- WebDAV配置测试 -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-server"></i> WebDAV连接测试</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        $webdavConfigs = $config->getWebDAVConfigs();
                        if (empty($webdavConfigs)): ?>
                            <div class="alert alert-warning">没有配置WebDAV存储</div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($webdavConfigs as $alias => $webdavConfig): ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6><?= htmlspecialchars($webdavConfig['name']) ?></h6>
                                                <p class="text-muted small"><?= htmlspecialchars($webdavConfig['url']) ?></p>
                                                <button class="btn btn-sm btn-outline-primary test-webdav" data-alias="<?= $alias ?>">
                                                    <i class="bi bi-wifi"></i> 测试连接
                                                </button>
                                                <span class="test-result ms-2"></span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志查看 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-file-text"></i> 系统日志</h5>
                        <button class="btn btn-sm btn-outline-primary" id="refreshLogs">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="logsContainer">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 测试WebDAV连接
        document.querySelectorAll('.test-webdav').forEach(button => {
            button.addEventListener('click', async function() {
                const alias = this.dataset.alias;
                const resultSpan = this.nextElementSibling;
                
                this.disabled = true;
                this.innerHTML = '<i class="bi bi-hourglass-split"></i> 测试中...';
                
                try {
                    const response = await fetch(`?action=test_webdav&alias=${alias}`);
                    const result = await response.json();
                    
                    if (result.success) {
                        resultSpan.innerHTML = '<span class="badge bg-success">连接成功</span>';
                    } else {
                        resultSpan.innerHTML = `<span class="badge bg-danger">连接失败: ${result.error}</span>`;
                    }
                } catch (error) {
                    resultSpan.innerHTML = '<span class="badge bg-danger">测试失败</span>';
                }
                
                this.disabled = false;
                this.innerHTML = '<i class="bi bi-wifi"></i> 测试连接';
            });
        });

        // 加载日志
        async function loadLogs() {
            const container = document.getElementById('logsContainer');
            
            try {
                const response = await fetch('?action=get_logs');
                const result = await response.json();
                
                if (result.success && result.logs.length > 0) {
                    let html = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>时间</th><th>级别</th><th>IP</th><th>消息</th></tr></thead><tbody>';
                    
                    result.logs.forEach(log => {
                        const levelClass = log.level === 'ERROR' ? 'danger' : (log.level === 'WARNING' ? 'warning' : 'info');
                        html += `<tr>
                            <td>${log.timestamp}</td>
                            <td><span class="badge bg-${levelClass}">${log.level}</span></td>
                            <td>${log.ip}</td>
                            <td>${log.message}</td>
                        </tr>`;
                    });
                    
                    html += '</tbody></table></div>';
                    container.innerHTML = html;
                } else {
                    container.innerHTML = '<div class="alert alert-info">暂无日志记录</div>';
                }
            } catch (error) {
                container.innerHTML = '<div class="alert alert-danger">加载日志失败</div>';
            }
        }

        // 刷新日志
        document.getElementById('refreshLogs').addEventListener('click', loadLogs);

        // 页面加载时获取日志
        loadLogs();
    </script>
</body>
</html>

<?php
// 处理退出登录
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: admin.php');
    exit;
}
?>
