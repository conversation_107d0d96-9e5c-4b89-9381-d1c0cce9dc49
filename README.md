# PHP WebDAV 文件床

一个基于WebDAV存储的PHP文件床程序，支持多存储账号配置，提供Web界面和API接口。

## 特性

- 🚀 支持多WebDAV存储账号配置
- 📁 拖拽上传，实时进度显示
- 🔗 一键复制下载链接
- 👀 文件预览功能
- 🔌 RESTful API支持
- 🛡️ 安全验证和限制
- 📊 上传日志记录
- 🎨 现代化响应式界面

## 安装

1. 克隆项目到服务器
```bash
git clone <repository-url>
cd php-webdav-filebed
```

2. 安装依赖
```bash
composer install
```

3. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置WebDAV账号信息
```

4. 创建必要目录
```bash
mkdir -p logs temp
chmod 755 logs temp
```

## 配置

编辑 `.env` 文件，配置WebDAV存储账号：

```env
# 默认使用的WebDAV配置
DEFAULT_WEBDAV=teracloud

# WebDAV配置示例
WEBDAV_TERACLOUD_NAME="TeraCloud存储"
WEBDAV_TERACLOUD_URL="https://kamo.teracloud.jp/dav/"
WEBDAV_TERACLOUD_USERNAME="your_username"
WEBDAV_TERACLOUD_PASSWORD="your_password"
WEBDAV_TERACLOUD_BASE_PATH="/uploads/"
```

## 使用

### Web界面
访问 `index.php` 使用Web界面上传文件

### API接口

#### 上传文件
```bash
curl -X POST -F "file=@example.jpg" -F "webdav=teracloud" http://your-domain/api.php
```

#### 获取WebDAV配置列表
```bash
curl http://your-domain/api.php?action=webdav_list
```

## 许可证

MIT License
