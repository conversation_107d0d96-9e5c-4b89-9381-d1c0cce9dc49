<?php
session_start();

// 重新加载环境变量
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $lines = explode("\n", $envContent);
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) {
            continue;
        }
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

$adminPassword = $_ENV['ADMIN_PASSWORD'] ?? 'admin123';

// 处理登录
if (isset($_POST['password'])) {
    if ($_POST['password'] === $adminPassword) {
        $_SESSION['admin_authenticated'] = true;
        echo "<div style='color: green;'>登录成功！Session已设置。</div>";
    } else {
        echo "<div style='color: red;'>密码错误！</div>";
    }
}

// 检查认证状态
$isAuthenticated = isset($_SESSION['admin_authenticated']) && $_SESSION['admin_authenticated'];

// 处理AJAX测试
if (isset($_GET['action']) && $_GET['action'] === 'test_auth') {
    header('Content-Type: application/json');
    
    $debugInfo = [
        'session_id' => session_id(),
        'session_data' => $_SESSION,
        'cookies' => $_COOKIE,
        'is_authenticated' => $isAuthenticated,
        'admin_password' => $adminPassword,
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'unknown',
        'php_version' => PHP_VERSION,
        'session_save_path' => session_save_path(),
        'session_name' => session_name(),
        'session_cookie_params' => session_get_cookie_params(),
        'headers_sent' => headers_sent(),
        'session_status' => session_status()
    ];
    
    if ($isAuthenticated) {
        echo json_encode(['success' => true, 'message' => '认证成功', 'debug' => $debugInfo]);
    } else {
        echo json_encode(['success' => false, 'error' => '未认证', 'debug' => $debugInfo]);
    }
    exit;
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XREA环境调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-info { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        button { padding: 10px 15px; margin: 5px; }
        pre { background: #f0f0f0; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>XREA环境调试工具</h1>
    
    <div class="debug-info">
        <h3>当前状态</h3>
        <p><strong>认证状态:</strong> <?= $isAuthenticated ? '<span class="success">已认证</span>' : '<span class="error">未认证</span>' ?></p>
        <p><strong>Session ID:</strong> <?= session_id() ?></p>
        <p><strong>PHP版本:</strong> <?= PHP_VERSION ?></p>
        <p><strong>服务器软件:</strong> <?= $_SERVER['SERVER_SOFTWARE'] ?? 'unknown' ?></p>
        <p><strong>Session状态:</strong> <?= session_status() ?> (1=禁用, 2=启用, 3=活跃)</p>
    </div>

    <?php if (!$isAuthenticated): ?>
    <div class="debug-info">
        <h3>登录测试</h3>
        <form method="post">
            <input type="password" name="password" placeholder="输入管理员密码" required>
            <button type="submit">登录</button>
        </form>
    </div>
    <?php endif; ?>

    <div class="debug-info">
        <h3>AJAX认证测试</h3>
        <button onclick="testAuth()">测试AJAX认证</button>
        <div id="authResult"></div>
    </div>

    <div class="debug-info">
        <h3>Session数据</h3>
        <pre><?= htmlspecialchars(print_r($_SESSION, true)) ?></pre>
    </div>

    <div class="debug-info">
        <h3>Cookie数据</h3>
        <pre><?= htmlspecialchars(print_r($_COOKIE, true)) ?></pre>
    </div>

    <div class="debug-info">
        <h3>Session配置</h3>
        <pre><?= htmlspecialchars(print_r(session_get_cookie_params(), true)) ?></pre>
        <p><strong>Session保存路径:</strong> <?= session_save_path() ?></p>
        <p><strong>Session名称:</strong> <?= session_name() ?></p>
    </div>

    <script>
        async function testAuth() {
            const resultDiv = document.getElementById('authResult');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const response = await fetch('?action=test_auth');
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = '<div class="success">✓ AJAX认证成功</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">✗ AJAX认证失败: ' + result.error + '</div>';
                }
                
                // 显示调试信息
                resultDiv.innerHTML += '<pre>' + JSON.stringify(result.debug, null, 2) + '</pre>';
                
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">✗ 请求失败: ' + error.message + '</div>';
            }
        }
    </script>
</body>
</html>
